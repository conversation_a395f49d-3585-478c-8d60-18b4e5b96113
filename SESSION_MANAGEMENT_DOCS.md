# Session Management System Documentation

## Overview

The session management system has been successfully implemented to complement the existing JWT authentication system. This provides both stateless (JWT) and stateful (session) authentication capabilities with persistent session storage and automatic cleanup.

## 🏗️ Architecture

### Database Schema

**Sessions Table:**
```sql
CREATE TABLE sessions (
    session_id CHAR(36) PRIMARY KEY,           -- UUID session identifier
    user_id CHAR(36) NOT NULL,                -- Foreign key to users table
    expires_at DATETIME NOT NULL,             -- Session expiration timestamp
    device_info TEXT,                         -- JSON device/browser information
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at DATETIME,               -- Updated on each validation
    FOREIGN KEY(user_id) REFERENCES users (id) ON DELETE CASCADE
);
```

### Hybrid Authentication System

- **JWT Tokens**: Contain user email and session_id
- **Session Records**: Persistent database storage with expiration
- **Validation**: Both JWT and session must be valid
- **Security**: Device fingerprinting and automatic cleanup

## 📁 File Structure

### New Files Created:
```
app/
├── models/
│   └── session.py              # Session SQLAlchemy model
├── schemas/
│   └── session.py              # Session Pydantic schemas
├── routes/
│   └── session.py              # Session management endpoints
└── utils/
    └── session.py              # Session utility functions
```

### Modified Files:
- `app/models/user.py` - Added sessions relationship
- `app/models/__init__.py` - Import Session model
- `app/schemas/__init__.py` - Import session schemas
- `app/main.py` - Include session router, updated CORS
- `app/routes/auth.py` - Integrated session creation/termination
- `app/utils/dependencies.py` - Added session-based auth dependency
- `app/utils/auth.py` - Added session-aware JWT functions
- `app/config.py` - Added session configuration

## 🔧 Configuration

### Environment Variables (.env)
```bash
# Session Management
SESSION_EXPIRE_HOURS=168                    # 7 days default
SESSION_CLEANUP_INTERVAL_HOURS=24          # Daily cleanup
```

### CORS Configuration
Updated to allow only localhost:3000 for frontend access:
```python
allow_origins=["http://localhost:3000"]
```

## 🚀 API Endpoints

### Authentication Endpoints (Modified)

#### POST /auth/login
**Enhanced with session creation**
- Creates both JWT token and session record
- JWT token includes session_id
- Captures device information

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

#### POST /auth/logout
**Enhanced with session termination**
- Terminates all user sessions
- Returns count of terminated sessions

### Session Management Endpoints (New)

#### GET /sessions/
**Get active sessions for current user**
```json
{
  "sessions": [
    {
      "session_id": "uuid-string",
      "user_id": "uuid-string",
      "expires_at": "2025-06-16T21:14:52.588287",
      "device_info": "{\"user_agent\": \"...\", \"ip\": \"...\"}",
      "created_at": "2025-06-09T21:14:52.588287",
      "last_accessed_at": "2025-06-09T21:15:58.783942"
    }
  ],
  "total": 1
}
```

#### DELETE /sessions/terminate
**Terminate specific session**
```json
{
  "session_id": "uuid-to-terminate"
}
```

#### DELETE /sessions/terminate-all
**Terminate all user sessions**

#### DELETE /sessions/terminate-others
**Terminate all sessions except current one**

#### POST /sessions/cleanup
**Clean up expired sessions (admin function)**

## 🔒 Security Features

### Session Security
- **UUID Session IDs**: Unpredictable identifiers
- **Device Fingerprinting**: IP address and user agent tracking
- **Automatic Expiration**: Configurable session lifetime
- **Cascade Deletion**: Sessions deleted when user is deleted
- **Last Access Tracking**: Monitor session activity

### Authentication Flow
1. User logs in → JWT + Session created
2. Each request → JWT validated + Session validated
3. Session updates last_accessed_at timestamp
4. Expired sessions automatically cleaned up
5. Logout → All sessions terminated

## 🛠️ Usage Examples

### Creating Session-Aware Dependencies
```python
from app.utils.dependencies import get_current_user_with_session

@router.get("/protected")
def protected_endpoint(
    user_session: tuple[User, Session] = Depends(get_current_user_with_session)
):
    user, session = user_session
    # Both user and session are validated
    return {"user": user.email, "session": str(session.session_id)}
```

### Session Utilities
```python
from app.utils.session import (
    create_session, validate_session, terminate_session,
    get_user_sessions, cleanup_expired_sessions
)

# Create session
session = create_session(db, user, device_info)

# Validate session
valid_session = validate_session(db, session_id)

# Get user sessions
sessions = get_user_sessions(db, user_id)

# Cleanup expired
cleaned_count = cleanup_expired_sessions(db)
```

## 🧪 Testing

### Functional Tests
- `test_session_functionality.py` - Core session operations
- Database operations (create, validate, terminate, cleanup)
- Timezone handling for SQLite compatibility

### API Tests
- Session creation during login
- Session validation and listing
- Session termination
- Cleanup operations

## 🔄 Session Lifecycle

1. **Creation**: During login with device info capture
2. **Validation**: On each authenticated request
3. **Update**: Last accessed timestamp updated
4. **Expiration**: Automatic cleanup of expired sessions
5. **Termination**: Manual or logout-triggered removal

## 📊 Database Compatibility

### SQLite Considerations
- Timezone-naive datetime storage
- Custom GUID type for UUID compatibility
- Proper foreign key constraints

### PostgreSQL Ready
- Native UUID support
- Timezone-aware datetime handling
- Optimized for production use

## 🚨 Important Notes

1. **Backward Compatibility**: Existing JWT-only tokens still work
2. **Session Expiration**: Default 7 days (configurable)
3. **Cleanup**: Automatic expired session removal
4. **Device Tracking**: JSON storage for security monitoring
5. **Cascade Deletion**: Sessions removed when user deleted

## 🔮 Future Enhancements

- Background task for automatic cleanup
- Session analytics and monitoring
- Rate limiting per session
- Geographic session tracking
- Session transfer between devices
