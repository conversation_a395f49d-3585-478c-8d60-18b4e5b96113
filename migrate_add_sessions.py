#!/usr/bin/env python3
"""
Migration script to add session management to existing database.
This script creates the sessions table and updates the database schema.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import engine, Base
from app.models.user import User
from app.models.session import Session
from sqlalchemy import text
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_database():
    """Add session management to existing database."""
    print("🔄 Database Migration: Adding Session Management")
    print("=" * 50)
    
    try:
        # Check if sessions table already exists
        with engine.connect() as conn:
            result = conn.execute(text(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='sessions'"
            ))
            if result.fetchone():
                print("✅ Sessions table already exists. No migration needed.")
                return
        
        print("📋 Creating sessions table...")
        
        # Create all tables (this will only create missing ones)
        Base.metadata.create_all(bind=engine)
        
        # Verify the sessions table was created
        with engine.connect() as conn:
            result = conn.execute(text(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='sessions'"
            ))
            if result.fetchone():
                print("✅ Sessions table created successfully!")
                
                # Show the schema
                schema_result = conn.execute(text("PRAGMA table_info(sessions)"))
                columns = schema_result.fetchall()
                print("\n📊 Sessions table schema:")
                for col in columns:
                    print(f"   - {col[1]} ({col[2]})")
                
                # Show foreign key constraints
                fk_result = conn.execute(text("PRAGMA foreign_key_list(sessions)"))
                foreign_keys = fk_result.fetchall()
                if foreign_keys:
                    print("\n🔗 Foreign key constraints:")
                    for fk in foreign_keys:
                        print(f"   - {fk[3]} -> {fk[2]}.{fk[4]}")
                
            else:
                print("❌ Failed to create sessions table!")
                return False
        
        print("\n🎉 Migration completed successfully!")
        print("\nNext steps:")
        print("1. Restart your application")
        print("2. Test the new session endpoints at /docs")
        print("3. Update your frontend to use the new session features")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def rollback_migration():
    """Rollback session management (remove sessions table)."""
    print("🔄 Rolling back session management...")
    
    try:
        with engine.connect() as conn:
            # Check if table exists
            result = conn.execute(text(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='sessions'"
            ))
            if not result.fetchone():
                print("✅ Sessions table doesn't exist. Nothing to rollback.")
                return True
            
            # Drop the sessions table
            conn.execute(text("DROP TABLE sessions"))
            conn.commit()
            print("✅ Sessions table dropped successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Rollback failed: {e}")
        return False

def show_current_schema():
    """Show current database schema."""
    print("📊 Current Database Schema")
    print("=" * 30)
    
    try:
        with engine.connect() as conn:
            # Get all tables
            result = conn.execute(text(
                "SELECT name FROM sqlite_master WHERE type='table'"
            ))
            tables = result.fetchall()
            
            for table in tables:
                table_name = table[0]
                print(f"\n🗂️  Table: {table_name}")
                
                # Get table schema
                schema_result = conn.execute(text(f"PRAGMA table_info({table_name})"))
                columns = schema_result.fetchall()
                for col in columns:
                    nullable = "NULL" if col[3] == 0 else "NOT NULL"
                    default = f" DEFAULT {col[4]}" if col[4] else ""
                    pk = " PRIMARY KEY" if col[5] == 1 else ""
                    print(f"   - {col[1]} {col[2]} {nullable}{default}{pk}")
                
                # Get foreign keys
                fk_result = conn.execute(text(f"PRAGMA foreign_key_list({table_name})"))
                foreign_keys = fk_result.fetchall()
                if foreign_keys:
                    print("   Foreign Keys:")
                    for fk in foreign_keys:
                        print(f"     - {fk[3]} -> {fk[2]}.{fk[4]}")
                
                # Get indexes
                idx_result = conn.execute(text(f"PRAGMA index_list({table_name})"))
                indexes = idx_result.fetchall()
                if indexes:
                    print("   Indexes:")
                    for idx in indexes:
                        print(f"     - {idx[1]} (unique: {bool(idx[2])})")
                        
    except Exception as e:
        print(f"❌ Failed to show schema: {e}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Session Management Migration Tool")
    parser.add_argument("action", choices=["migrate", "rollback", "schema"], 
                       help="Action to perform")
    
    args = parser.parse_args()
    
    if args.action == "migrate":
        migrate_database()
    elif args.action == "rollback":
        rollback_migration()
    elif args.action == "schema":
        show_current_schema()
