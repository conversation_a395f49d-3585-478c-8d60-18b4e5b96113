#!/usr/bin/env python3
"""
Test script to verify session management API endpoints.
This script tests the API endpoints using FastAPI's test client.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from app.main import app
from app.database import SessionLocal, engine, Base
from app.models.user import User
from app.utils.auth import get_password_hash
import json

def test_session_api():
    """Test the session management API endpoints."""
    print("🧪 Testing Session Management API")
    print("=" * 40)

    # Create test client
    client = TestClient(app)
    
    # Test data
    test_user_data = {
        "firstName": "Test",
        "lastName": "User",
        "email": "<EMAIL>",
        "password": "TestPass123!",
        "confirmPassword": "TestPass123!"
    }
    
    login_data = {
        "email": "<EMAIL>",
        "password": "TestPass123!"
    }
    
    try:
        # 1. Register a test user
        print("1. Registering test user...")
        response = client.post("/auth/register", json=test_user_data)
        print(f"   Status: {response.status_code}")
        if response.status_code == 201:
            user_info = response.json()
            print(f"   ✅ User registered: {user_info['email']}")
        else:
            print(f"   ❌ Registration failed: {response.text}")
            return
        
        # 2. Login to create session
        print("\n2. Logging in to create session...")
        response = client.post("/auth/login", json=login_data)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data["access_token"]
            print(f"   ✅ Login successful, token received")
        else:
            print(f"   ❌ Login failed: {response.text}")
            return
        
        # 3. Test getting current user info (to verify token works)
        print("\n3. Getting current user info...")
        headers = {"Authorization": f"Bearer {access_token}"}
        response = client.get("/auth/me", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            user_info = response.json()
            print(f"   ✅ User info retrieved: {user_info['name']}")
        else:
            print(f"   ❌ Failed to get user info: {response.text}")
        
        # 4. Test getting active sessions
        print("\n4. Getting active sessions...")
        response = client.get("/sessions/", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            sessions_data = response.json()
            print(f"   ✅ Found {sessions_data['total']} active sessions")
            if sessions_data['sessions']:
                session_id = sessions_data['sessions'][0]['session_id']
                print(f"   📝 First session ID: {session_id}")
        else:
            print(f"   ❌ Failed to get sessions: {response.text}")
            return
        
        # 5. Login again to create another session
        print("\n5. Creating second session...")
        response = client.post("/auth/login", json=login_data)
        if response.status_code == 200:
            print("   ✅ Second session created")
        
        # 6. Check sessions again
        print("\n6. Checking sessions after second login...")
        response = client.get("/sessions/", headers=headers)
        if response.status_code == 200:
            sessions_data = response.json()
            print(f"   ✅ Now found {sessions_data['total']} active sessions")
        
        # 7. Test session termination
        if 'session_id' in locals():
            print("\n7. Terminating specific session...")
            terminate_data = {"session_id": session_id}
            response = client.delete("/sessions/terminate", json=terminate_data, headers=headers)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ {result['message']}")
            else:
                print(f"   ❌ Failed to terminate session: {response.text}")
        
        # 8. Test cleanup endpoint
        print("\n8. Testing session cleanup...")
        response = client.post("/sessions/cleanup", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ {result['message']}")
            print(f"   🧹 Cleaned {result['cleaned_sessions']} expired sessions")
        
        # 9. Test logout (terminates all sessions)
        print("\n9. Testing logout...")
        response = client.post("/auth/logout", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ {result['message']}")
        
        # 10. Verify sessions are terminated
        print("\n10. Verifying sessions after logout...")
        response = client.get("/sessions/", headers=headers)
        if response.status_code == 200:
            sessions_data = response.json()
            print(f"   ✅ Found {sessions_data['total']} active sessions (should be 0)")
        
        print("\n🎉 All API tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ API test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup: Delete test user
        print("\n11. Cleaning up test user...")
        db = SessionLocal()
        try:
            test_user = db.query(User).filter(User.email == "<EMAIL>").first()
            if test_user:
                db.delete(test_user)
                db.commit()
                print("   ✅ Test user cleaned up")
        except Exception as e:
            print(f"   ⚠️  Cleanup warning: {e}")
        finally:
            db.close()

if __name__ == "__main__":
    test_session_api()
