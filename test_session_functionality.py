#!/usr/bin/env python3
"""
Test script to verify session management functionality.
This script tests the basic session operations without running the full server.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timezone, timedelta
from app.database import SessionLocal, engine, Base
from app.models.user import User
from app.models.session import Session
from app.utils.auth import get_password_hash
from app.utils.session import (
    create_session, validate_session, terminate_session,
    get_user_sessions, cleanup_expired_sessions
)
import uuid

def test_session_functionality():
    """Test the session management functionality."""
    print("🧪 Testing Session Management Functionality")
    print("=" * 50)
    
    # Create database session
    db = SessionLocal()
    
    try:
        # Create a test user
        print("1. Creating test user...")
        test_user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password=get_password_hash("testpassword123")
        )
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        print(f"   ✅ User created: {test_user.email} (ID: {test_user.id})")
        
        # Test session creation
        print("\n2. Creating session...")
        device_info = '{"user_agent": "Test Browser", "ip": "127.0.0.1"}'
        session = create_session(db, test_user, device_info)
        print(f"   ✅ Session created: {session.session_id}")
        print(f"   📅 Expires at: {session.expires_at}")
        
        # Test session validation
        print("\n3. Validating session...")
        validated_session = validate_session(db, str(session.session_id))
        if validated_session:
            print(f"   ✅ Session is valid: {validated_session.session_id}")
            print(f"   🕒 Last accessed: {validated_session.last_accessed_at}")
        else:
            print("   ❌ Session validation failed")
        
        # Test getting user sessions
        print("\n4. Getting user sessions...")
        user_sessions = get_user_sessions(db, str(test_user.id))
        print(f"   ✅ Found {len(user_sessions)} active sessions")
        
        # Create another session
        print("\n5. Creating second session...")
        session2 = create_session(db, test_user, '{"user_agent": "Mobile Browser"}')
        print(f"   ✅ Second session created: {session2.session_id}")
        
        # Test getting multiple sessions
        user_sessions = get_user_sessions(db, str(test_user.id))
        print(f"   ✅ Now found {len(user_sessions)} active sessions")
        
        # Test session termination
        print("\n6. Terminating first session...")
        terminated = terminate_session(db, str(session.session_id))
        if terminated:
            print("   ✅ Session terminated successfully")
        else:
            print("   ❌ Session termination failed")
        
        # Verify termination
        user_sessions = get_user_sessions(db, str(test_user.id))
        print(f"   ✅ Now found {len(user_sessions)} active sessions")
        
        # Test expired session cleanup
        print("\n7. Testing expired session cleanup...")
        # Create an expired session manually
        expired_session = Session(
            user_id=test_user.id,
            expires_at=datetime.now(timezone.utc) - timedelta(hours=1),  # Expired 1 hour ago
            device_info='{"user_agent": "Expired Browser"}'
        )
        db.add(expired_session)
        db.commit()
        
        # Run cleanup
        cleaned_count = cleanup_expired_sessions(db)
        print(f"   ✅ Cleaned up {cleaned_count} expired sessions")
        
        print("\n🎉 All session functionality tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup test data
        print("\n8. Cleaning up test data...")
        db.query(Session).filter(Session.user_id == test_user.id).delete()
        db.query(User).filter(User.email == "<EMAIL>").delete()
        db.commit()
        db.close()
        print("   ✅ Test data cleaned up")

if __name__ == "__main__":
    test_session_functionality()
